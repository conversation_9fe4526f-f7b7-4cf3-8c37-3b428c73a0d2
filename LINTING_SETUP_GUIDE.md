# ESLint & Prettier Setup Guide

## 🎯 Overview

This project is now configured with a professional linting setup that includes:

- **ESLint** for code quality and consistency
- **Prettier** for code formatting
- **TypeScript** integration
- **React** specific rules
- **Next.js** optimizations

## 🔧 Configuration Files

### `.eslintrc.json`

```json
{
  "extends": [
    "next/core-web-vitals",
    "plugin:@typescript-eslint/recommended",
    "plugin:react/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "rules": {
    // Custom rules for better code quality
    "@typescript-eslint/no-unused-vars": [
      "warn",
      {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_",
        "caughtErrorsIgnorePattern": "^_"
      }
    ],
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off"
  }
}
```

### `.prettierrc`

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": false,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "always",
  "endOfLine": "lf",
  "plugins": ["prettier-plugin-tailwindcss"]
}
```

## 📜 Available Scripts

### Linting Commands

```bash
# Run ESLint with auto-fix
npm run lint

# Run ESLint without auto-fix (check only)
npm run lint:check

# Format code with Prettier
npm run format

# Check if code is properly formatted
npm run format:check

# Type checking with TypeScript
npm run type-check
```

## 🚀 Usage

### Daily Development Workflow

1. **Before committing code:**

   ```bash
   npm run lint
   npm run type-check
   ```

2. **Format all files:**

   ```bash
   npm run format
   ```

3. **Check code quality:**
   ```bash
   npm run lint:check
   ```

### IDE Integration

#### VS Code

Install these extensions for the best experience:

- ESLint
- Prettier - Code formatter
- TypeScript and JavaScript Language Features

#### Settings

Add to your VS Code `settings.json`:

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

## 🔍 What Gets Checked

### ESLint Rules

- **TypeScript**: Type safety, unused variables, proper imports
- **React**: JSX best practices, hooks rules, component patterns
- **Next.js**: Performance optimizations, proper imports
- **Code Quality**: Consistent formatting, naming conventions

### Prettier Formatting

- **Indentation**: 2 spaces
- **Quotes**: Double quotes for strings
- **Semicolons**: Always required
- **Line Length**: 80 characters max
- **Trailing Commas**: ES5 compatible

## ⚠️ Common Issues & Solutions

### 1. Unused Variables

**Warning**: `'variable' is defined but never used`

**Solution**:

- Remove unused imports/variables
- Or prefix with underscore: `_unusedVariable`

### 2. Missing Dependencies in useEffect

**Warning**: `React Hook useEffect has missing dependencies`

**Solution**:

- Add missing dependencies to dependency array
- Or use ESLint disable comment if intentional

### 3. Prettier Formatting

**Error**: `Replace 'text' with "text"`

**Solution**: Run `npm run format` to auto-fix

## 🎯 Benefits

### Code Quality

- ✅ Catches potential bugs early
- ✅ Enforces consistent coding standards
- ✅ Improves code readability
- ✅ Reduces code review time

### Team Collaboration

- ✅ Consistent code style across team
- ✅ Automated formatting
- ✅ Reduced merge conflicts
- ✅ Better maintainability

## 🔧 Customization

### Adding New Rules

Edit `.eslintrc.json` to add custom rules:

```json
{
  "rules": {
    "your-custom-rule": "error"
  }
}
```

### Disabling Rules

For specific lines:

```javascript
// eslint-disable-next-line rule-name
const problematicCode = something();
```

For entire files:

```javascript
/* eslint-disable rule-name */
```

## 📊 Current Status

✅ **ESLint**: Configured and working
✅ **Prettier**: Configured and working  
✅ **TypeScript**: Integrated with ESLint
✅ **React Rules**: Enabled
✅ **Next.js Rules**: Enabled
✅ **Auto-fix**: Enabled with `--fix` flag

The linting setup is now professional and ready for production use!
