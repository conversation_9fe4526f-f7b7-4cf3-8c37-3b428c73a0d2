import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import _ from "lodash";
import { toast } from "sonner";

import {
  AccountPlanData,
  AccountPlanTableType,
} from "@/features/account-plan/types";
import DataTable from "@/components/ui/data-table";
import {
  DatePickerCell,
  EditableNumberCell,
  TiptapCell,
} from "@/components/ui/data-table/data-table-components";
import { APCurrentOpportunity } from "@/features/account-plan/types/revenue-types";
import { Button } from "@/components/ui/button";
import { useCreateCurrentOpportunity } from "@/features/account-plan/api/revenue-apis/current-opportunity/create-current-opportunity";
import { useUpdateCurrentOpportunity } from "@/features/account-plan/api/revenue-apis/current-opportunity/update-current-opportunity";
import { useDeleteCurrentOpportunity } from "@/features/account-plan/api/revenue-apis/current-opportunity/delete-current-opportunity";
import { useCurrentOpportunityList } from "@/features/account-plan/api/revenue-apis/current-opportunity/get-current-opportunity-list";
import { AccountTable } from "../base-table";

export const CurrentOpportunitiesTable = ({
  accountPlan,
}: {
  accountPlan?: AccountPlanData;
}) => {
  const [tableData, setTableData] = useState<APCurrentOpportunity[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { currentOpportunityList } = useCurrentOpportunityList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const createCurrentOpportunity = useCreateCurrentOpportunity({});
  const updateCurrentOpportunity = useUpdateCurrentOpportunity({
    mutationConfig: {
      invalidate: true,
    },
  });
  const deleteCurrentOpportunity = useDeleteCurrentOpportunity({});

  useEffect(() => {
    if (!currentOpportunityList) return;

    const newTableData = currentOpportunityList?.map((v, idx) => ({
      idx,
      ...v,
    }));

    setTableData(newTableData);
  }, [currentOpportunityList]);

  const selectedRows = Object.keys(rowSelection)
    .filter((rowId) => rowSelection[rowId])
    .map((idx) => tableData[parseInt(idx)]);

  const onAddRow = async () => {
    try {
      const res = await createCurrentOpportunity.mutateAsync({
        accountId,
      });

      setTableData((prev) => [...prev, res.data]);
    } catch (_) {
      toast("An unexpected error occured when adding data");
    }
  };

  const onDeleteRows = async () => {
    try {
      const promises = [];

      setTableData(
        tableData.filter((row) => !selectedRows.find((v) => v.id === row.id))
      );

      setRowSelection({});

      promises.push(
        selectedRows.map(async (row) => {
          if (!!row?.id) {
            return deleteCurrentOpportunity.mutateAsync({
              id: row.id,
              accountId,
            });
          }
        })
      );
      await Promise.all(promises);
    } catch (_) {
      toast("An unexpected error occured when deleting rows");
    }
  };

  const onChangeData = useCallback(
    async (data: Partial<APCurrentOpportunity>, id: number) => {
      try {
        setTableData((prev) =>
          prev.map((v) => (v.id === id ? { ...v, ...data } : v))
        );

        await updateCurrentOpportunity.mutateAsync({
          accountId,
          id,
          data,
        });
      } catch (_) {
        toast("An unexpected error occured when modifying data");
      }
    },
    [accountId, updateCurrentOpportunity]
  );

  const columns: ColumnDef<APCurrentOpportunity>[] = useMemo(
    () => [
      {
        accessorKey: "product_service_name",
        header: "Service and Product Description",
        size: 575,
        cell: ({ row }) => {
          const rowData = row.original;

          return (
            <TiptapCell
              value={rowData.product_service_name}
              onChange={(product_service_name) =>
                onChangeData({ product_service_name }, rowData.id)
              }
              placeholder="Input service and product description"
              className="text-lg"
            />
          );
        },
        meta: {
          tooltip:
            "The specific products or services being proposed or discussed as part of the current opportunity.",
        },
      },
      {
        accessorKey: "value",
        header: "Currency Value",
        size: 200,
        cell: ({ row }) => {
          const rowData = row.original;

          return (
            <EditableNumberCell
              value={rowData.value}
              onChange={(value) => {
                onChangeData({ value }, rowData.id);
              }}
              className="text-lg"
            />
          );
        },
        meta: {
          tooltip:
            "The estimated monetary value of the ongoing opportunity, reflecting its potential contribution to revenue.",
        },
      },
      {
        accessorKey: "close_date",
        header: "Close Date",
        size: 225,
        cell: ({ row }) => {
          const rowData = row.original;

          return (
            <DatePickerCell
              date={rowData.close_date}
              onSelect={(close_date) => {
                onChangeData(
                  { close_date: close_date as unknown as string },
                  rowData.id
                );
              }}
              className="text-lg"
            />
          );
        },
        meta: {
          tooltip:
            "The expected date when the opportunity is anticipated to close.",
        },
      },
    ],
    [onChangeData]
  );

  return (
    <AccountTable
      type={AccountPlanTableType.CURRENT_OPPORTUNITY}
      footer={
        <>
          <Button
            onClick={onAddRow}
            isLoading={createCurrentOpportunity.isPending}
          >
            Add row
          </Button>
          <Button
            variant="destructive"
            disabled={selectedRows.length === 0}
            onClick={onDeleteRows}
          >
            Delete Row
          </Button>
        </>
      }
    >
      <DataTable
        columns={columns}
        data={tableData}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
        currency={accountPlan?.account_plan_group?.currency}
      />
    </AccountTable>
  );
};
