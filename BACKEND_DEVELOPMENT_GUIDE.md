# Backend Development Guide - Working with Fly.io Hosted API

## 🚨 Current Situation Analysis

**YES, your setup is completely dependent on the online Fly.io backend.**

### Current Architecture:

```
Local Frontend (Next.js) → Fly.io Backend API → Fly.io PostgreSQL Database
     localhost:3000           psai-api-wispy-resonance-3660.fly.dev    perceptionselling-prod-db.fly.dev
```

### Implications:

- ❌ **No offline development** - Can't work without internet
- ❌ **Shared backend** - Changes affect all developers
- ❌ **Production data risk** - Directly connected to live database
- ❌ **No backend debugging** - Can't step through backend code
- ❌ **Deployment dependency** - Must deploy to test backend changes

## 🎯 Solutions & Development Approaches

### Option 1: Get Backend Source Code (Recommended)

You need to obtain the backend source code to run it locally.

#### Step 1: Locate Backend Repository

```bash
# Check if you have access to the backend repo
# Common locations:
# - Same GitHub organization as frontend
# - Separate repository (psai-api, perception-selling-api, etc.)
# - Private repository you need access to
```

#### Step 2: Clone Backend Repository

```bash
git clone <backend-repository-url>
cd <backend-project-name>
```

#### Step 3: Set Up Local Backend Environment

```bash
# Install dependencies (varies by technology)
# For Ruby on Rails:
bundle install

# For Node.js:
npm install

# For Python:
pip install -r requirements.txt

# For Go:
go mod download
```

### Option 2: Download Backend from Fly.io (If Possible)

```bash
# Try to get the source code from Fly.io
fly ssh console -a psai-api-wispy-resonance-3660

# Once inside, explore the structure
ls -la
cat Dockerfile  # This will tell you the technology stack
```

## 🔍 Identifying Backend Technology Stack

Based on your Fly.io secrets, your backend appears to be **Ruby on Rails**:

### Evidence:

- `RAILS_MASTER_KEY` - Rails encryption key
- `SIDEKIQ_USERNAME/PASSWORD` - Background job processor for Rails
- `REDIS_URL` - Used by Sidekiq for job queuing
- `MAILER_SMTP_SERVER` - Rails ActionMailer configuration

### ✅ CONFIRMED: Your Backend Structure (Ruby 3.1.2):

```
psai-api/ (Ruby 3.1.2)
├── app/
│   ├── auth/           # Authentication logic
│   ├── controllers/    # API endpoints
│   ├── jobs/          # Background jobs
│   ├── mailers/       # Email functionality
│   ├── models/        # Database models
│   ├── repositories/  # Data access layer
│   ├── services/      # Business logic
│   └── views/         # JSON responses
├── config/
│   ├── database.yml
│   ├── routes.rb
│   └── environments/
├── db/
│   ├── migrate/
│   └── seeds.rb
├── Gemfile
├── Dockerfile
└── fly.toml
```

## 🛠️ Setting Up Local Backend Development

### Step 1: Prerequisites

```bash
# For Rails backend:
# Install Ruby 3.1.2 (use rbenv or rvm)
rbenv install 3.1.2  # EXACT version needed
gem install bundler

# Install PostgreSQL locally
# Windows: Download from postgresql.org
# Mac: brew install postgresql
# Linux: sudo apt-get install postgresql

# Install Redis
# Windows: Download from redis.io
# Mac: brew install redis
# Linux: sudo apt-get install redis-server
```

### Step 2: Database Setup

```bash
# Create local database
createdb psai_development
createdb psai_test

# Configure database.yml
# config/database.yml
development:
  adapter: postgresql
  database: psai_development
  username: your_username
  password: your_password
  host: localhost
  port: 5432

# Run migrations
rails db:migrate
rails db:seed  # If there are seed files
```

### Step 3: Environment Configuration

```bash
# Create .env file in backend root
# .env
DATABASE_URL=postgresql://username:password@localhost:5432/psai_development
REDIS_URL=redis://localhost:6379
RAILS_ENV=development

# Copy production secrets you need (be careful!)
# Get from: fly secrets list -a psai-api-wispy-resonance-3660
S3_ACCESS_KEY_ID=your_key
S3_SECRET_ACCESS_KEY=your_secret
S3_BUCKET=your_bucket
S3_REGION=your_region
```

### Step 4: Start Local Backend

```bash
# Start Redis
redis-server

# Start Rails server
rails server -p 8000  # Run on port 8000

# Start background jobs (in another terminal)
bundle exec sidekiq
```

### Step 5: Update Frontend Configuration

```bash
# Update .env.local to point to local backend
NEXT_PUBLIC_API_URL=http://localhost:8000/v1
```

## 🔄 Development Workflow Options

### Option A: Full Local Development (Ideal)

```
Local Frontend → Local Backend → Local Database
   localhost:3000    localhost:8000    localhost:5432
```

**Pros**: Complete control, offline development, safe testing
**Cons**: Requires backend source code and setup

### Option B: Hybrid Development (Compromise)

```
Local Frontend → Staging Backend → Staging Database
   localhost:3000    staging.fly.dev    staging-db.fly.dev
```

**Pros**: Don't need local backend setup
**Cons**: Still online dependency, shared staging environment

### Option C: Frontend-Only Development (Current)

```
Local Frontend → Production Backend → Production Database
   localhost:3000    psai-api-wispy-resonance-3660.fly.dev    perceptionselling-prod-db.fly.dev
```

**Pros**: Quick setup, no backend knowledge needed
**Cons**: All the issues you're experiencing

## 📋 Action Plan to Get Backend Source Code

### 1. Check Your Access

```bash
# List all your Fly.io apps
fly apps list

# Check if you can access the backend
fly ssh console -a psai-api-wispy-resonance-3660
```

### 2. Contact Your Team

- Ask for backend repository access
- Request development environment setup guide
- Get local development documentation

### 3. Alternative: Create Development Backend

If you can't get the source code, you might need to:

- Create a staging/development instance on Fly.io
- Clone the production database to a development database
- Use the development backend for local frontend development

## 🚀 Next Steps

1. **Immediate**: Try to access backend via SSH and explore structure
2. **Short-term**: Get backend repository access from your team
3. **Long-term**: Set up complete local development environment

## 🔥 CRITICAL: How to Get Your Backend Source Code

### Method 1: Check for Git Repository

```bash
# SSH into your backend
fly ssh console -a psai-api-wispy-resonance-3660

# Check if it's a git repository
ls -la .git/
git remote -v  # This will show you the repository URL
```

### Method 2: Download/Clone from Repository

If you find the git remote URL, you can clone it locally:

```bash
git clone <repository-url>
cd <backend-project>
```

### Method 3: Contact Your Team/Organization

- Check your GitHub/GitLab organization for a backend repository
- Look for repositories named: `psai-api`, `perception-selling-api`, `backend`, etc.
- Ask team members for repository access

### Method 4: Create Development Environment on Fly.io

If you can't get source code, create a separate development backend:

```bash
# Clone the production app to development
fly apps create psai-api-dev
fly postgres create psai-dev-db
```

Would you like me to help you explore the backend structure via SSH or guide you through any of these steps?
