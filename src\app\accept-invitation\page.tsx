"use client";

import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod";
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation";
import React, { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/input/password-input";
import { useUserManagementAcceptInvitation } from "@/features/user-management/api/invitations/accept-invitation";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { IconAlertCircle } from "@tabler/icons-react";
import { isRequestError } from "@/lib/api-client";
import { useInvitationCodeDetails } from "@/features/user-management/api/invitations/get-invitation-code-details";
import { useLogin } from "@/features/auth/api/login";
import { PATH } from "@/constants/path";
import { useIsHydrated } from "@/lib/hooks/use-hydrated";
import { Checkbox } from "@/components/ui/checkbox";

const formSchema = z.object({
  email: z.string().optional(),
  name: z.string().min(1, { message: "Please enter your name" }),
  last_name: z.string().optional(),
  password: z.string().min(8, "Password minimum contain 8 characters"),
});

type FormSchema = z.infer<typeof formSchema>;

function AcceptInvitationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isHydrated } = useIsHydrated();

  const [checked, setChecked] = useState(false);

  const invite_code = searchParams.get("invite_code");

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    reValidateMode: "onSubmit",
  });
  const { invitationCodeDetails } = useInvitationCodeDetails({ invite_code });
  const acceptInvitation = useUserManagementAcceptInvitation({});
  const login = useLogin({});
  const [errorNotif, setErrorNotif] = useState<string>("");

  const invitationDetail = useMemo(() => {
    return invitationCodeDetails?.[0] ?? null;
  }, [invitationCodeDetails]);

  const onAccept = async (values: FormSchema) => {
    if (!invite_code) {
      setErrorNotif("Invitation code not found");
      return;
    }

    try {
      const res = await acceptInvitation.mutateAsync({
        data: {
          invite_code,
          ...values,
        },
      });

      await login.mutateAsync({
        email: invitationDetail?.email ?? "",
        password: values.password,
        organization_unique_id: res.data.organization_unique_id,
      });

      router.push(PATH.DASHBOARD);

      toast("Invitation has been successfully accepted");
    } catch (e) {
      if (isRequestError(e)) {
        const errorMessage = e.response?.data.errors[0].message ?? "";
        setErrorNotif(errorMessage);
      }
    }
  };

  if (!isHydrated) return null;

  return (
    <div className="flex h-full w-full flex-col items-center justify-center">
      <div className="flex min-w-[30vw] flex-col items-center justify-center gap-4 rounded-xl border border-neutral-300 bg-white p-8 shadow-lg">
        <p className="text-2xl font-semibold text-primary-500">
          You have been invited !
        </p>

        {!!invitationDetail && (
          <div className="my-2 h-4 text-sm text-primary-500">
            <p>
              <strong>{invitationDetail?.invited_by?.first_name}</strong> has
              invited you to join{" "}
              <strong>{invitationDetail?.organization?.name}</strong>
            </p>
          </div>
        )}

        <Form {...form}>
          <form
            className="mt-4 min-w-[20vw]"
            onSubmit={form.handleSubmit(onAccept)}
          >
            {errorNotif && (
              <Alert variant="destructive" className="relative mb-4">
                <IconAlertCircle className="h-4 w-4" />
                <AlertTitle className="font-bold">
                  Create Account Failed
                </AlertTitle>
                <AlertDescription>{errorNotif}</AlertDescription>
              </Alert>
            )}

            <FormItem className="mb-8">
              <FormControl>
                <Input
                  id="email"
                  placeholder="Email Address"
                  className="w-full"
                  disabled
                  value={invitationDetail?.email}
                />
              </FormControl>
              <FormMessage />
            </FormItem>

            <div className="mb-8 grid gap-8">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        id="name"
                        placeholder="Enter your first name"
                        className="w-full"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="last_name"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        id="last_name"
                        placeholder="Enter your last name"
                        className="w-full"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <PasswordInput
                        id="password"
                        className="w-full"
                        placeholder="Enter your password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="mb-8 flex items-center gap-2 text-primary-500">
              <Checkbox
                checked={checked}
                onCheckedChange={() => setChecked((prev) => !prev)}
              />

              <div className="text-sm">
                Accept <span className="font-semibold">Terms & Conditions</span>
              </div>
            </div>

            <Button
              type="submit"
              isLoading={acceptInvitation.isPending}
              className="w-full"
            >
              Create Account
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
}

export default AcceptInvitationPage;
