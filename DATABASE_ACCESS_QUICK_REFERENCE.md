# Database Access Quick Reference

## 🎯 Quick Database Connection

### **Primary Database**: `psai-prod`

```bash
# Fastest way to connect
fly postgres connect -a perceptionselling-prod-db --database psai-prod
```

### **External Connection String**

```bash
# Get actual credentials first
fly secrets list -a psai-api-wispy-resonance-3660

# Then use this format
postgresql://USERNAME:<EMAIL>:5432/psai-prod?sslmode=require
```

## 🔑 Getting Database Credentials

### **Method 1: From Backend Secrets**

```bash
fly secrets list -a psai-api-wispy-resonance-3660
```

Look for:

- `PSAI_API_DATABASE_USERNAME`
- `PSAI_API_DATABASE_PASSWORD`
- `PSAI_API_DATABASE_DBNAME`
- `PSAI_API_DATABASE_HOSTNAME`

### **Method 2: From Database Secrets**

```bash
fly secrets list -a perceptionselling-prod-db
```

## 🛠️ Common Database Operations

### **Connect and Explore**

```sql
-- List all databases
\l

-- Connect to psai-prod database
\c psai-prod

-- List all tables
\dt

-- Describe a table
\d table_name

-- Show table sizes
SELECT schemaname,tablename,pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname NOT IN ('information_schema','pg_catalog')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### **User Management**

```sql
-- List all users
\du

-- Check current user
SELECT current_user;

-- Check user permissions
\dp
```

## 📊 Database Information

| Property         | Value                               |
| ---------------- | ----------------------------------- |
| **Host**         | `perceptionselling-prod-db.fly.dev` |
| **Port**         | `5432`                              |
| **Primary DB**   | `psai-prod`                         |
| **SSL Mode**     | `require`                           |
| **Machine Size** | performance-1x-cpu@2048MB           |
| **Region**       | sin (Singapore)                     |

## 🔒 Security Notes

- ✅ **SSL Required**: All external connections must use SSL
- ✅ **Authentication Required**: Username/password needed
- ✅ **Network Secured**: TLS encryption in transit
- ⚠️ **Production Data**: Handle with extreme care
- ⚠️ **Backup First**: Always backup before modifications

## 🚨 Emergency Access

If you can't connect normally:

```bash
# SSH into backend and access database from there
fly ssh console -a psai-api-wispy-resonance-3660
rails console
# Then use ActiveRecord to query database
```

## 📱 GUI Database Tools

Configure your favorite database tool with:

- **Host**: `perceptionselling-prod-db.fly.dev`
- **Port**: `5432`
- **Database**: `psai-prod`
- **SSL Mode**: `Require`
- **Username**: (from secrets)
- **Password**: (from secrets)

Popular tools:

- pgAdmin
- DBeaver
- TablePlus
- DataGrip
