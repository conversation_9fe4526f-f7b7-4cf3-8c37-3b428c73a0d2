import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import _ from "lodash";
import { toast } from "sonner";

import {
  AccountPlanData,
  AccountPlanTableType,
} from "@/features/account-plan/types";
import DataTable from "@/components/ui/data-table";
import {
  DatePickerCell,
  EditableNumberCell,
  TiptapCell,
} from "@/components/ui/data-table/data-table-components";
import { APCurrentRevenue } from "@/features/account-plan/types/revenue-types";

import { AccountTable, AccountTableTitle } from "../base-table";
import { Button } from "@/components/ui/button";
import { useCreateCurrentRevenue } from "@/features/account-plan/api/revenue-apis/current-revenue/create-current-revenue";
import { useUpdateCurrentRevenue } from "@/features/account-plan/api/revenue-apis/current-revenue/update-current-revenue";
import { useDeleteCurrentRevenue } from "@/features/account-plan/api/revenue-apis/current-revenue/delete-current-revenue";
import { useCurrentRevenueList } from "@/features/account-plan/api/revenue-apis/current-revenue/get-current-revenue-list";
import { useWalletShareList } from "@/features/account-plan/api/position-apis/wallet-share/get-wallet-share-list";
import { APWalletShareType } from "@/features/account-plan/types/position-types";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { useAvailableWalletSize } from "../hooks/use-available-wallet";

export const CurrentRevenueTable = ({
  accountPlan,
}: {
  accountPlan?: AccountPlanData;
}) => {
  const [tableData, setTableData] = useState<APCurrentRevenue[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [visitedDescriptionIds, setVisitedDescriptionIds] = useState<string[]>(
    []
  );
  const [visitedDateIds, setVisitedDateIds] = useState<string[]>([]);

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { currentRevenueList, totalCurrentRevenue } = useAvailableWalletSize();
  const { walletShareList } = useWalletShareList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const createCurrentRevenue = useCreateCurrentRevenue({});
  const updateCurrentRevenue = useUpdateCurrentRevenue({
    mutationConfig: {
      invalidate: true,
    },
  });
  const deleteCurrentRevenue = useDeleteCurrentRevenue({});

  const walletSize = useMemo(
    () =>
      (walletShareList?.find((v) => v.item_type === APWalletShareType.OURS)
        ?.shared_type_analysis ?? 0) - totalCurrentRevenue,
    [walletShareList, totalCurrentRevenue]
  );

  useEffect(() => {
    if (!currentRevenueList) return;

    const newTableData = currentRevenueList?.map((v, idx) => ({
      idx,
      ...v,
    }));

    setTableData(newTableData);
  }, [currentRevenueList]);

  const selectedRows = Object.keys(rowSelection)
    .filter((rowId) => rowSelection[rowId])
    .map((idx) => tableData[parseInt(idx)]);

  const onAddRow = async () => {
    try {
      const res = await createCurrentRevenue.mutateAsync({
        accountId,
      });

      setTableData((prev) => [...prev, res.data]);
    } catch (_) {
      toast("An unexpected error occured when adding data");
    }
  };

  const onDeleteRows = async () => {
    try {
      const promises = [];

      setTableData(
        tableData.filter((row) => !selectedRows.find((v) => v.id === row.id))
      );

      setRowSelection({});

      promises.push(
        selectedRows.map(async (row) => {
          if (!!row?.id) {
            return deleteCurrentRevenue.mutateAsync({
              id: row.id,
              accountId,
            });
          }
        })
      );
      await Promise.all(promises);
    } catch (_) {
      toast("An unexpected error occured when deleting rows");
    }
  };

  const onChangeData = useCallback(
    async (data: Partial<APCurrentRevenue>, id: number) => {
      try {
        setTableData((prev) =>
          prev.map((v) => (v.id === id ? { ...v, ...data } : v))
        );

        await updateCurrentRevenue.mutateAsync({
          accountId,
          id,
          data,
        });
      } catch (_) {
        toast("An unexpected error occured when modifying data");
      }
    },
    [accountId, updateCurrentRevenue]
  );

  const columns: ColumnDef<APCurrentRevenue>[] = useMemo(
    () => [
      {
        accessorKey: "product_service_name",
        header: "Service and Product Description",
        size: 575,
        cell: ({ row }) => {
          const rowData = row.original;

          return (
            <TiptapCell
              className="text-lg"
              required
              visited={visitedDescriptionIds.includes(row.id)}
              value={rowData.product_service_name}
              onChange={(product_service_name) => {
                onChangeData({ product_service_name }, rowData.id);
                setVisitedDescriptionIds((prevIds) =>
                  _.union(prevIds, [row.id])
                );
              }}
              placeholder="Input Service and Product Description"
              message="You need a Service and Product Description for your Existing Revenue"
            />
          );
        },
        meta: {
          tooltip:
            "The specific products or services that are currently contributing to the account's revenue.",
        },
      },
      {
        accessorKey: "value",
        header: "Currency Value",
        size: 200,
        cell: ({ row }) => {
          const rowData = row.original;

          return (
            <EditableNumberCell
              className="text-lg"
              value={rowData.value}
              onChange={(value) => {
                onChangeData({ value }, rowData.id);
              }}
              handleMaxErrorMessage={(maxValue) =>
                `Value cannot exceed ${maxValue} (Remaining from Existing Wallet Size)`
              }
              maxValue={walletSize + rowData.value}
            />
          );
        },
        meta: {
          tooltip:
            "The total monetary amount of revenue being generated from the account at present.",
        },
      },
      {
        accessorKey: "renewal_date",
        header: "Renewal Date",
        size: 225,
        cell: ({ row }) => {
          const rowData = row.original;

          return (
            <DatePickerCell
              className="text-lg"
              required
              placeholder="Pick a Date"
              message="Date input is missing"
              date={rowData.renewal_date}
              visited={visitedDateIds.includes(row.id)}
              onBlur={() => {
                setVisitedDateIds((prevIds) => _.union(prevIds, [row.id]));
              }}
              onSelect={(renewal_date) => {
                onChangeData(
                  { renewal_date: renewal_date as unknown as string },
                  rowData.id
                );
              }}
            />
          );
        },
        meta: {
          tooltip:
            "The date when the current contract, subscription, or agreement is up for renewal, signaling potential for retention or upselling.",
        },
      },
    ],
    [onChangeData, visitedDateIds, visitedDescriptionIds, walletSize]
  );

  return (
    <AccountTable
      type={AccountPlanTableType.CURRENT_REVENUE}
      footer={
        <>
          <Button onClick={onAddRow} isLoading={createCurrentRevenue.isPending}>
            Add row
          </Button>
          <Button
            variant="destructive"
            disabled={selectedRows.length === 0}
            onClick={onDeleteRows}
          >
            Delete Row
          </Button>
        </>
      }
    >
      <DataTable
        columns={columns}
        data={tableData}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
        currency={accountPlan?.account_plan_group?.currency}
      />
    </AccountTable>
  );
};
