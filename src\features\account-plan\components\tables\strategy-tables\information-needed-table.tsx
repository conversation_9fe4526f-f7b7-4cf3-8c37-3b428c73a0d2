import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useMemo } from "react";
import _ from "lodash";
import { toast } from "sonner";

import { AccountPlanTableType } from "@/features/account-plan/types";
import { getUTCOffset } from "@/lib/utils";
import DataTable from "@/components/ui/data-table";
import { APMissingInformation } from "@/features/account-plan/types/strategy-types";
import { useGenerateMissingInformation } from "@/features/account-plan/api/strategy-apis/missing-information/create-missing-information-generate";
import { useMissingInformationList } from "@/features/account-plan/api/strategy-apis/missing-information/get-missing-information-list";
import { TiptapCell } from "@/components/ui/data-table/data-table-components";

import { AccountTable, AccountTableTitle } from "../base-table";

export const InformationNeededTable = () => {
  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { missingInformationList } = useMissingInformationList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const generateMissingInformation = useGenerateMissingInformation({});

  const onGenerateInformationNeeded = async () => {
    try {
      await generateMissingInformation.mutateAsync({
        accountId,
        data: {
          tz: getUTCOffset(),
        },
      });

      toast("Successfully generated the information needed");
    } catch (_) {
      toast("An error occured while generating the information needed");
    }
  };

  const columns: ColumnDef<APMissingInformation>[] = useMemo(
    () => [
      {
        accessorKey: "description",
        header: "",
        cell: ({ row }) => {
          const rowData = row.original;

          return (
            <TiptapCell
              value={rowData.description ?? ""}
              onChange={() => {}}
              editable={false}
            />
          );
        },
      },
    ],
    []
  );

  return (
    <AccountTable type={AccountPlanTableType.MISSING_INFORMATION}>
      <DataTable
        showHeader={false}
        columns={columns}
        data={missingInformationList ?? []}
        headerClassName="!text-center"
        emptyMessage={
          <p className="mt-[17.5vh] text-3xl text-neutral-300">
            Generate analysis to view Information Needed
          </p>
        }
      />
    </AccountTable>
  );
};
