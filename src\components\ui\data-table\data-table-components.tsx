import { ChevronDown } from "lucide-react";
import * as React from "react";

import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { Button } from "../button";
import { Tiptap } from "../tiptap";
import { DatePicker } from "../date-picker";
import { formatDecimalValue } from "@/lib/utils/table-utils";

type MultiSelectProps = {
  options: {
    value: string;
    label: string;
    disabled?: boolean;
    excludes?: string[];
  }[];
  selected: string[];
  onChange: (selected: string[]) => void;
  open: boolean;
  onOpenChange: (value: boolean) => void;
  isPreview?: boolean;
  className?: string;
};

export function MultiSelectCell({
  options,
  selected,
  onChange,
  open,
  onOpenChange,
  isPreview,
  className,
}: MultiSelectProps) {
  const popoverRef = React.useRef<HTMLDivElement | null>(null);
  const triggerRef = React.useRef<HTMLButtonElement | null>(null);
  const [tempSelected, setTempSelected] = React.useState<string[]>(selected);

  React.useEffect(() => {
    setTempSelected(selected);
  }, [selected]);

  return (
    <Popover open={open && !isPreview}>
      <PopoverTrigger
        asChild
        ref={triggerRef}
        className="h-full"
        onClick={() => {
          if (isPreview) return;

          onOpenChange(!open);
        }}
      >
        <div
          className={cn(
            "flex h-auto w-full cursor-pointer items-start border-transparent px-res-x-sm py-res-y-base text-2xl",
            className
          )}
          style={{ backgroundColor: "#f5f5f5" }}
          onMouseEnter={(e) =>
            (e.currentTarget.style.backgroundColor = "#f5f5f5")
          }
          onMouseLeave={(e) =>
            (e.currentTarget.style.backgroundColor = "#f5f5f5")
          }
        >
          {selected.length > 0 ? (
            <div className="flex flex-wrap gap-res-x-xs">
              {selected.map((v, idx) => (
                <Badge
                  key={idx}
                  variant="outline"
                  className={cn(
                    "rounded-[1vw] px-res-x-xs py-res-y-xs text-2xl font-normal",
                    className
                  )}
                  style={{ backgroundColor: "#f5f5f5" }}
                >
                  {v}
                </Badge>
              ))}
            </div>
          ) : (
            "Select role(s)"
          )}
          <ChevronDown className="ml-auto mt-[0.7vh] size-res-x-sm shrink-0 opacity-50" />
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-56" ref={popoverRef}>
        {options.map((option) => {
          const isDisabled = option.excludes?.some((excluded) =>
            tempSelected.includes(excluded)
          );

          return (
            <label
              key={option.value}
              className="flex items-center space-x-2 px-2 py-1.5"
              style={{ backgroundColor: "#f5f5f5" }}
              onMouseEnter={(e) =>
                (e.currentTarget.style.backgroundColor = "#f5f5f5")
              }
              onMouseLeave={(e) =>
                (e.currentTarget.style.backgroundColor = "#f5f5f5")
              }
            >
              <Checkbox
                disabled={isDisabled && !tempSelected.includes(option.value)}
                checked={tempSelected.includes(option.value)}
                onCheckedChange={() => {
                  if (isPreview || isDisabled) return;

                  setTempSelected((prev) => {
                    if (prev.includes(option.value)) {
                      return prev.filter((item) => item !== option.value);
                    } else {
                      return [
                        ...prev.filter(
                          (item) => !option.excludes?.includes(item)
                        ),
                        option.value,
                      ];
                    }
                  });
                }}
              />
              <span className={cn(isDisabled && "text-neutral-200")}>
                {option.label}
              </span>
            </label>
          );
        })}
        <div className="mt-2 flex justify-end gap-2 p-2">
          <Button
            variant="ghost"
            onClick={() => {
              setTempSelected(selected);
              onOpenChange(false);
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              onChange(tempSelected);
              onOpenChange(false);
            }}
          >
            OK
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}

type SelectCellProps = {
  value?: string | null;
  onChange: (value: string) => void;
  options: { value: string; label: string; disabled?: boolean }[];
  placeholder?: string;
  isPreview?: boolean;
  className?: string;
  error?: string;
};

export function SelectCell({
  value,
  onChange,
  options,
  placeholder = "Select one",
  isPreview,
  className,
}: SelectCellProps) {
  const [open, setOpen] = React.useState(false);

  return (
    <Select
      open={open && !isPreview}
      value={options.find((v) => v.value === value)?.value ?? undefined}
      onValueChange={onChange}
      onOpenChange={setOpen}
    >
      <SelectTrigger
        className={cn(
          "flex h-full w-full cursor-pointer items-start text-wrap rounded-none border-transparent px-res-x-sm py-res-y-base text-left text-xl [&>span]:line-clamp-none",
          className
        )}
        style={{ backgroundColor: "#f5f5f5" }}
        onClick={() => {
          if (!isPreview) setOpen((prev) => !prev);
        }}
        iconClassName="mt-[0.7vh]"
      >
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>

      <SelectContent>
        {options.map((option) => (
          <SelectItem
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

type EditableCellProps = {
  value: string;
  onChange: (value: string) => void;
} & Omit<React.ComponentProps<typeof Input>, "value" | "onChange">;

export function EditableCell({
  value,
  onChange,
  className,
  ...props
}: EditableCellProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [editValue, setEditValue] = React.useState<string>(value);

  const handleBlur = () => {
    setIsEditing(false);
    onChange(editValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      setIsEditing(false);
      onChange(editValue);
    } else if (e.key === "Escape") {
      setIsEditing(false);
      setEditValue(value);
    }
  };

  if (isEditing) {
    return (
      <Input
        className={cn(
          "h-auto overflow-hidden px-res-x-sm py-res-y-base align-top text-3xl outline-none",
          className
        )}
        value={editValue}
        onChange={(e) => setEditValue(e.target.value)}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        autoFocus
        {...props}
      />
    );
  }

  return (
    <div
      className={cn(
        "h-full cursor-pointer border-transparent px-res-x-sm py-res-y-base",
        className
      )}
      onClick={() => setIsEditing(true)}
    >
      {value}
    </div>
  );
}

type EditableNumberCellProps = {
  value: number;
  onChange: (value: number) => void;
  handleMinErrorMessage?: (value: string) => string;
  handleMaxErrorMessage?: (value: string) => string;
  minValue?: number;
  maxValue?: number;
} & Omit<React.ComponentProps<typeof Input>, "value" | "onChange">;

export function EditableNumberCell({
  value,
  onChange,
  className,
  minValue,
  maxValue,
  handleMinErrorMessage,
  handleMaxErrorMessage,
  ...props
}: EditableNumberCellProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [editValue, setEditValue] = React.useState<number>(value);
  const [error, setError] = React.useState<string | null>(null);

  const handleBlur = () => {
    const newValue = isNaN(editValue) ? 0 : editValue;

    if (typeof minValue === "number" && newValue < minValue) {
      const decimalMinValue = formatDecimalValue({ value: minValue });

      setError(
        handleMinErrorMessage
          ? handleMinErrorMessage(decimalMinValue)
          : `Value cannot be less than ${decimalMinValue}`
      );

      setEditValue(value);
      return;
    } else {
      onChange(newValue);
      setIsEditing(false);
      setError(null);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleBlur();
    }
  };

  const handleOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseInt(e.target.value);

    if (typeof maxValue === "number" && newValue > maxValue) {
      const decimalMaxValue = formatDecimalValue({
        value: maxValue ?? 0,
      });

      setError(
        !!handleMaxErrorMessage
          ? handleMaxErrorMessage(decimalMaxValue)
          : `Value cannot exceed ${decimalMaxValue}`
      );
    } else {
      setError(null);
      setEditValue(newValue);
    }
  };

  if (isEditing && !props.disabled) {
    return (
      <div className="relative">
        <Input
          type="number"
          className={cn(
            "h-full overflow-hidden px-res-x-sm py-res-y-base text-xl outline-none",
            className
          )}
          value={editValue}
          onChange={handleOnChange}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          autoFocus
          {...props}
        />
        {error && (
          <p className="absolute top-full mt-1 text-xs text-red-500">{error}</p>
        )}
      </div>
    );
  }

  return (
    <div
      className={cn("border-transparent px-res-x-sm py-res-y-base", className)}
      onClick={() => setIsEditing(true)}
    >
      {formatDecimalValue({
        value: value ?? 0,
      })}
    </div>
  );
}

type EditableTextareaCellProps = {
  value: string;
  onChange: (value: string) => void;
} & Omit<React.ComponentProps<typeof Textarea>, "value" | "onChange">;

export function EditableTextareaCell({
  value,
  onChange,
  className,
  ...props
}: EditableTextareaCellProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [editValue, setEditValue] = React.useState(value);
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);

  React.useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [isEditing]);

  const handleBlur = () => {
    setIsEditing(false);
    onChange(editValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      setIsEditing(false);
      onChange(editValue);
    } else if (e.key === "Escape") {
      setIsEditing(false);
      setEditValue(value);
    }
  };

  if (isEditing) {
    return (
      <Textarea
        ref={textareaRef}
        className={cn(
          "h-full resize-none overflow-hidden px-res-x-sm py-res-y-base text-3xl outline-none",
          className
        )}
        value={editValue}
        onChange={(e) => setEditValue(e.target.value)}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        autoFocus
        {...props}
      />
    );
  }

  return (
    <div
      className="h-full border-transparent px-res-x-sm py-res-y-base"
      onClick={() => setIsEditing(true)}
    >
      {value}
    </div>
  );
}

export function TiptapCell({
  value,
  onChange,
  placeholder,
  required,
  className,
  message,
  visited,
  editable,
}: React.ComponentProps<typeof Tiptap> & {
  required?: boolean;
  message?: string;
  visited?: boolean;
}) {
  const editValueRef = React.useRef(value);

  const [isEditing, setIsEditing] = React.useState(false);

  React.useEffect(() => {
    editValueRef.current = value;
  }, [value]);

  const isNotFilled = visited && required && !editValueRef.current;

  return (
    <Tiptap
      className={cn(
        "h-full resize-none overflow-hidden px-res-x-sm py-res-y-base text-xl outline-none",
        isEditing ? "border-none" : "border-none",
        isNotFilled && "min-h-[11vh]",
        className
      )}
      placeholderClassName={cn(isNotFilled && "before:text-red-500")}
      value={editValueRef.current}
      onChange={(val) => {
        editValueRef.current = val;
      }}
      onFocus={() => setIsEditing(true)}
      onBlur={() => {
        setIsEditing(false);
        onChange(editValueRef.current);
      }}
      placeholder={isNotFilled ? message : placeholder}
      mentionSuggestion={[]}
      editable={editable}
    />
  );
}

export function DatePickerCell(
  props: React.ComponentProps<typeof DatePicker> & {
    required?: boolean;
    visited?: boolean;
    message?: string;
  }
) {
  const isNotFilled = props.visited && props.required && !props.date;

  return (
    <div className="my-res-y-base flex">
      <DatePicker
        {...props}
        placeholder={isNotFilled ? props.message : props.placeholder}
        className={cn(
          "border-none text-3xl",
          isNotFilled && "text-red-500",
          props.className
        )}
        icon={false}
        format={props.format ?? "DD/MM/YYYY"}
      />
    </div>
  );
}
