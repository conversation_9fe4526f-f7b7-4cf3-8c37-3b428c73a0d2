# Login Setup Guide - Connecting Local Development to Fly.io Backend

## 🎯 Problem Summary

The login was failing locally with various errors:

1. `TypeError: Cannot read properties of undefined (reading '0')`
2. CORS policy errors
3. API connection issues

But the same credentials worked perfectly on the production Vercel deployment.

## ⚠️ CRITICAL WARNING ⚠️

**NEVER MODIFY THE FLY.IO BACKEND DIRECTLY!**

- All fixes must be done in the frontend code only
- Do not change secrets, deploy, or restart the backend
- The backend is working perfectly for production

## 🔧 Root Causes & Solutions

### 1. **API URL Path Mismatch** ⚠️ **CRITICAL FIX**

**Problem**: The local environment was using the wrong API path.

**Local (Wrong)**:

```bash
NEXT_PUBLIC_API_URL=https://psai-api-wispy-resonance-3660.fly.dev/api
```

**Production (Correct)**:

```bash
NEXT_PUBLIC_API_URL=https://psai-api-wispy-resonance-3660.fly.dev/v1
```

**Solution**: Updated `.env.local` to use `/v1` path to match production.

### 2. **CORS Configuration Issues** 🌐 **BACKEND FIX (COMPLETED)**

**Problem**: The backend CORS settings were misconfigured 7 hours ago, breaking production.

**Root Cause**: Release #43 added incorrect CORS settings that blocked requests.

**Solution Applied to Backend** (DO NOT REPEAT):

- Removed problematic CORS setting
- Added correct CORS for production domain: `https://app.perceptionselling.ai`
- Ensured database points to correct instance with user data

**Status**: ✅ Backend is now working perfectly for production

### 3. **Error Handling Bug** 🐛 **FRONTEND FIX**

**Problem**: The login form expected error response in format:

```javascript
e.response?.data.errors[0].message;
```

But the API might return different error structures, causing the `undefined` error.

**Solution**: Enhanced error handling with proper TypeScript safety:

```javascript
// Before (Fragile - could cause undefined error)
const errorMessage = e.response?.data.errors[0].message ?? "";

// After (Safe - follows codebase pattern with optional chaining)
const errorMessage = e.response?.data.errors[0]?.message ?? "";
```

This ensures:

- **Type Safety**: Matches the `ErrorResponse` type definition
- **Consistency**: Follows the pattern used throughout the codebase
- **Safety**: Uses optional chaining to prevent undefined errors

### 4. **CORS Credentials Issue** 🌐 **FRONTEND FIX**

**Problem**: `withCredentials: true` in API client caused CORS issues in local development.

**Solution**: Made credentials conditional based on environment:

```javascript
// Before (Caused CORS issues locally)
config.withCredentials = true;

// After (Environment-specific)
config.withCredentials = process.env.NODE_ENV === "production";
```

This ensures:

- **Production**: Uses credentials (required for backend CORS)
- **Local Development**: No credentials (avoids CORS issues)

### 5. **Local Development CORS Issue** 🌐 **FRONTEND PROXY FIX**

**Problem**: Backend only allows `https://app.perceptionselling.ai` but not `http://localhost:3000`.

**Solution**: Added Next.js development proxy to avoid CORS issues:

```javascript
// next.config.mjs - Development-only proxy
async rewrites() {
  return process.env.NODE_ENV === 'development' ? [
    {
      source: '/api/v1/:path*',
      destination: 'https://psai-api-wispy-resonance-3660.fly.dev/v1/:path*',
    },
  ] : [];
}
```

```bash
# .env.local - Use local proxy instead of direct backend
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
```

**Benefits**:

- ✅ No CORS issues in local development
- ✅ No backend modifications required
- ✅ Production remains unchanged

### 6. **React Controlled Input Warning** ⚠️ **FORM FIX**

**Problem**: Form fields without default values caused React warnings.

**Solution**: Added missing default values:

```javascript
// Before (Missing defaults)
defaultValues: {
  email: "",
  organization_unique_id: "",
},

// After (Complete defaults)
defaultValues: {
  email: "",
  organization_unique_id: "",
  password: "",
  remember_me: false,
},
```

## ✅ Final Working Configuration

### `.env.local` File:

```bash
# Backend API URL - Use local proxy to avoid CORS issues
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1

# Development environment flag
NODE_ENV=development

# Optional: Sentry error tracking
SENTRY_AUTH_TOKEN=
```

### `next.config.mjs` Configuration:

```javascript
// Development-only proxy configuration
async rewrites() {
  return process.env.NODE_ENV === 'development' ? [
    {
      source: '/api/v1/:path*',
      destination: 'https://psai-api-wispy-resonance-3660.fly.dev/v1/:path*',
    },
  ] : [];
}
```

### Test Credentials:

- **Organization ID**: DoStory
- **Email**: <EMAIL>
- **Password**: DoStoryPS@i

## 🚀 How to Set Up Login Successfully

### Step 1: Environment Configuration

1. Create/update `.env.local` in your project root
2. Set the correct API URL with `/v1` path (NOT `/api`)
3. Ensure the file is named `.env.local` (NOT `.local.env`)

### Step 2: Verify Backend Connection

Your Fly.io backend should be accessible at:

- **Backend API**: `https://psai-api-wispy-resonance-3660.fly.dev`
- **Database**: `perceptionselling-prod-db.fly.dev:5432`

### Step 3: Start Development Server

```bash
npm run dev
```

The server should show:

```
✓ Ready in X.Xs
- Environments: .env.local
```

### Step 4: Test Login

1. Navigate to `http://localhost:3000` (or 3001 if 3000 is busy)
2. Enter your credentials:
   - **Organization ID**: DoStory
   - **Email**: <EMAIL>
   - **Password**: DoStoryPS@i
3. Login should work without errors

### Step 5: Verify API Connection (Optional)

Open browser console and check Network tab during login:

- API calls should go to: `https://psai-api-wispy-resonance-3660.fly.dev/v1/login`
- Should NOT show CORS errors
- Response should be proper HTTP status (not 502 Bad Gateway)

## 🔍 Debugging Tips

### If Local Login Still Fails:

1. **Check Network Tab**: Verify API calls go to `/v1` endpoint
2. **Check Console**: Look for any remaining error messages
3. **Verify Credentials**: Ensure Organization ID, Email, and Password are correct
4. **Test Production First**: Confirm credentials work on `https://app.perceptionselling.ai/`
5. **Check Environment Loading**: Ensure Next.js shows "Environments: .env.local" on startup
6. **Restart Dev Server**: Sometimes environment changes require a restart

### Common Local Development Issues:

- **Wrong API Path**: Must use `/v1`, not `/api`
- **File Name**: Must be `.env.local`, not `.local.env`
- **Case Sensitivity**: Organization ID is case-sensitive
- **CORS Issues**: May occur due to `withCredentials: true` in API client
- **Environment Not Loading**: Restart development server after changing `.env.local`
- **Port Conflicts**: Try different port if 3000 is busy

### 🚨 If Local Still Fails But Production Works:

**DO NOT TOUCH THE BACKEND!** The issue is in your local frontend setup.

**Safe Local Fixes Only:**

1. Check `.env.local` file exists and has correct content
2. Restart development server: `npm run dev`
3. Clear browser cache and cookies
4. Check browser console for specific error messages
5. Verify API calls in Network tab point to correct URL

## 📊 Environment Comparison

| Environment               | API URL                               | Proxy                                                | Status     |
| ------------------------- | ------------------------------------- | ---------------------------------------------------- | ---------- |
| **Local Dev**             | `http://localhost:3000/api/v1`        | → `https://psai-api-wispy-resonance-3660.fly.dev/v1` | ✅ Working |
| **Vercel Production**     | `https://api.perceptionselling.ai/v1` | Direct                                               | ✅ Working |
| **Vercel Pre-Production** | `https://psai-api.fly.dev/v1`         | Direct                                               | ✅ Working |

## 🎉 Success Indicators

When everything is working correctly, you should see:

- ✅ No console errors
- ✅ Successful login redirect to dashboard
- ✅ API calls in Network tab show 200 status
- ✅ Authentication token stored properly

## 🛡️ Safety Guidelines

### ⚠️ NEVER DO THESE (Lessons Learned):

1. **DO NOT modify Fly.io backend secrets or configuration**
2. **DO NOT deploy or restart the backend**
3. **DO NOT change database connections on the backend**
4. **DO NOT modify CORS settings on the backend**

### ✅ SAFE LOCAL FIXES ONLY:

1. **Modify `.env.local` file only**
2. **Update frontend error handling**
3. **Restart local development server**
4. **Clear browser cache/cookies**
5. **Check browser console and network tab**

## 🔧 Current Issue Resolution (Local Development)

### Problem: Local login fails but production works

**Status**: Production is working perfectly ✅
**Issue**: Local development environment needs configuration fix

### Immediate Steps to Fix Local Login:

1. **Verify Environment File**:

   ```bash
   # Check .env.local exists and contains:
   NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
   NODE_ENV=development
   SENTRY_AUTH_TOKEN=
   ```

2. **Restart Development Server**:

   ```bash
   # Kill any running dev server
   # Then restart:
   npm run dev
   ```

3. **Check Server Output**:
   Look for this confirmation:

   ```
   ▲ Next.js 14.2.30
   - Local:        http://localhost:3000
   - Environments: .env.local
   ✓ Ready in X.Xs
   ```

4. **Test API Connection**:
   - Open browser console
   - Try login with test credentials
   - Check Network tab for API calls
   - Verify no CORS errors

### If Still Failing:

**Check these specific issues:**

1. **CORS Error**: If you see CORS policy errors, the `withCredentials: true` in API client might be causing issues
2. **Wrong URL**: Verify API calls go to `/v1` not `/api`
3. **Environment Loading**: Ensure Next.js shows "Environments: .env.local"
4. **Browser Cache**: Clear cache and cookies for localhost
5. **Port Issues**: Try different port if 3000 is busy

**Remember**: NEVER modify the backend - it's working perfectly for production!

---

**Key Takeaways**:

1. The main issue was the API path mismatch (`/api` vs `/v1`)
2. Backend issues require careful coordination - never modify production backend directly
3. Always test production first to confirm credentials work
4. Local issues are usually environment configuration problems
